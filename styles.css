/* Font Imports */
@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    line-height: 1.6;
    color: #112736;
    overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #fff8ef;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #4b879a, #112736);
    border-radius: 10px;
    border: 2px solid #fff8ef;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #112736, #04070f);
}

/* Header styles are now handled by header-and-footer.js */

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    padding-top: 0;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100%;
    min-height: 100vh;
}

.hero-text {
    background: #fff8ef;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 60px;
    position: relative;
}

.main-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: #4b879a; /* Petrol-Blau für Startseite */
    margin-bottom: 20px;
    letter-spacing: 3px;
    line-height: 1.1;
}

.subtitle {
    font-size: clamp(1.2rem, 2.5vw, 1.8rem);
    font-weight: 400;
    color: #112736;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.circle {
    color: #112736;
    font-size: 0.8em;
    font-weight: bold;
}

.hero-image {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 3px;
    animation: bounce 2s infinite;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: background-color 0.3s ease, opacity 0.3s ease;
    z-index: 10;
}

.scroll-indicator:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.scroll-arrow {
    width: 16px;
    height: 16px;
    border: none;
    border-right: 2px solid #ffffff;
    border-bottom: 2px solid #ffffff;
    transform: rotate(45deg);
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.scroll-indicator:hover .scroll-arrow {
    opacity: 1;
}

.scroll-arrow:nth-child(1) {
    animation-delay: 0s;
}

.scroll-arrow:nth-child(2) {
    animation-delay: 0.2s;
    margin-top: -8px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
        opacity: 0.7;
    }
    40% {
        transform: translateY(-8px);
        opacity: 1;
    }
    60% {
        transform: translateY(-4px);
        opacity: 0.9;
    }
}

/* Combined Preview Section */
.combined-preview-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4b879a 0%, #112736 100%);
    padding: 4rem 0;
}

.combined-preview-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    width: 100%;
    align-items: stretch;
}

.preview-card {
    background: rgba(255, 248, 239, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 248, 239, 0.2);
    display: flex;
    flex-direction: column;
}

.preview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 248, 239, 0.15);
}

.preview-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.preview-card:hover .preview-image img {
    transform: scale(1.05);
}

.preview-text {
    padding: 2rem;
    color: #fff8ef;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.preview-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #fff8ef;
}

.preview-description {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
    flex-grow: 1;
}

.preview-button {
    display: inline-block;
    background: #d4a574;
    color: #112736;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 50px; /* Runder Button */
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    align-self: flex-start;
    box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
}

.preview-button:hover {
    background: #c19660;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 165, 116, 0.4);
}










.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #112736; /* Dunkelblau für Startseite */
    margin-bottom: 1.5rem;
    line-height: 1.2;
}



.section-description {
    font-size: 1.2rem;
    color: #112736;
    font-weight: 300;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    opacity: 0.9;
}








/* Responsive Design for Combined Preview */
@media (max-width: 768px) {
    .combined-preview-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .preview-image {
        height: 200px;
    }

    .preview-text {
        padding: 1.5rem;
    }

    .preview-title {
        font-size: 1.5rem;
    }

    .preview-description {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 480px) {
    .combined-preview-section {
        padding: 2rem 0;
    }

    .combined-preview-content {
        gap: 2rem;
    }

    .preview-image {
        height: 180px;
    }

    .preview-text {
        padding: 1rem;
    }

    .preview-title {
        font-size: 1.3rem;
    }

    .preview-description {
        font-size: 0.85rem;
    }

    .preview-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
    }
}

/* Mobile and Tablet Responsive */
@media (max-width: 1024px) {
    .hero-content {
        display: flex !important;
        flex-direction: column !important;
        min-height: 100vh;
        grid-template-columns: none !important;
    }

    .hero-text {
        padding: 60px 30px 40px 30px; /* Mehr Padding oben für Hamburger-Menü */
        text-align: center;
        align-items: center;
        flex: 0 0 auto; /* Nimmt nur den benötigten Platz */
        min-height: 45vh; /* Mindesthöhe für den Textbereich */
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: #fff8ef !important; /* Sicherstellen, dass Background da ist */
        width: 100% !important;
    }

    .main-title {
        font-size: clamp(2rem, 8vw, 3rem);
        margin-bottom: 15px;
    }

    .subtitle {
        font-size: clamp(1rem, 4vw, 1.4rem);
        justify-content: center;
        text-align: center;
    }

    .hero-image {
        flex: 1; /* Nimmt den restlichen Platz */
        min-height: 60vh; /* Höher für bessere Abdeckung */
        width: 100%;
        overflow: hidden;
    }

    .hero-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        min-height: 60vh; /* Mindesthöhe für vollständige Abdeckung */
    }

    .scroll-indicator {
        bottom: 20px;
    }
}

@media (max-width: 480px) {
    .hero-text {
        padding: 60px 20px 20px 20px; /* Mehr Padding oben für Hamburger-Menü */
        min-height: 40vh; /* Kompakter für kleine Bildschirme */
    }

    .hero-image {
        min-height: 65vh; /* Höher für bessere Abdeckung auf kleinen Bildschirmen */
        overflow: hidden;
    }

    .hero-image img {
        min-height: 65vh;
    }

    .subtitle {
        gap: 10px;
    }
}

/* Impressum Page Styles */
.impressum-page {
    background: linear-gradient(135deg, #fff8ef 0%, #4b879a 100%);
    min-height: 100vh;
    padding-top: 80px;
}

.impressum-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
    background: #fff8ef;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(4,7,15,0.1);
    margin-top: 40px;
    margin-bottom: 40px;
}

.impressum-content h2 {
    color: #112736;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 30px;
    margin-top: 40px;
}

.impressum-content h2:first-child {
    margin-top: 0;
}

.impressum-content h4 {
    color: #112736;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 15px;
    margin-top: 30px;
}

.impressum-content p {
    color: #04070f;
    line-height: 1.8;
    margin-bottom: 20px;
    font-size: 1rem;
}

.contact-info {
    background: #4b879a;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #112736;
    margin-bottom: 20px;
}

.contact-info p {
    margin: 0;
    color: #fff8ef;
    font-weight: 500;
}

@media (max-width: 768px) {
    .impressum-page {
        padding-top: 60px;
    }

    .impressum-content {
        margin: 20px;
        padding: 30px 20px;
    }

    .impressum-content h2 {
        font-size: 1.5rem;
    }

    .impressum-content h4 {
        font-size: 1.1rem;
    }
}
