/* Retreat-Seite Spezifische Styles */

/* Hero-<PERSON><PERSON>ich Anpassungen für Retreat-Seite */
.retreat-page .hero-text {
    padding: 80px 120px 60px 120px; /* Mehr Abstand oben wegen Hamburger-Menü */
    align-items: flex-start;
}

.retreat-page .main-title {
    font-size: clamp(2.5rem, 5vw, 3.8rem); /* Optimale Größe für gute Balance */
    margin-bottom: 15px;
}

.retreat-page .subtitle {
    font-size: clamp(1.2rem, 2.5vw, 1.8rem); /* Proportional angepasst */
    margin-bottom: 30px;
}

/* Hero CTA Button for Retreat Page */
.retreat-page .hero-cta-button {
    display: inline-block;
    background: #d4a574;
    color: #112736;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 50px; /* <PERSON><PERSON> */
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
}

.retreat-page .hero-cta-button:hover {
    background: #c19660;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 165, 116, 0.4);
}

/* Experience Section */
.experience-section {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff8ef;
    padding: 4rem 0;
}

.experience-content {
    max-width: 1000px;
    width: 100%;
    text-align: center;
}

.experience-text {
    margin-top: 2rem;
    text-align: left;
}

.experience-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #112736;
}

/* Schedule Section */
.schedule-section {
    min-height: 100vh;
    background: linear-gradient(135deg, #112736 0%, #4b879a 100%);
    padding: 4rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.schedule-content {
    max-width: 1000px;
    width: 100%;
    text-align: center;
}

.schedule-intro {
    font-size: 1.1rem;
    color: #fff8ef;
    line-height: 1.7;
    text-align: center;
    margin-bottom: 3rem;
    opacity: 0.9;
}

/* Accordion Styles */
.accordion {
    max-width: 900px;
    margin: 0 auto;
}

.accordion-item {
    margin-bottom: 1rem;
    border-radius: 15px;
    overflow: hidden;
    background: rgba(255, 248, 239, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 248, 239, 0.2);
}

.accordion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 248, 239, 0.05);
}

.accordion-header:hover {
    background: rgba(255, 248, 239, 0.1);
}

.accordion-item.active .accordion-header {
    background: rgba(75, 135, 154, 0.2);
}

.day-number {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #fff8ef;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.day-title {
    color: #fff8ef;
    font-size: 1rem;
    font-weight: 500;
    margin-left: 1rem;
}

.accordion-icon {
    color: #fff8ef;
    font-size: 1.5rem;
    font-weight: 300;
    transition: transform 0.3s ease;
    min-width: 30px;
    text-align: center;
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(255, 248, 239, 0.05);
}

.accordion-content p {
    padding: 1.5rem 2rem;
    margin: 0;
    color: #fff8ef;
    line-height: 1.7;
    font-size: 1rem;
}

/* Connection Section */
.connection-section {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff8ef;
    padding: 4rem 0;
}

.connection-content {
    max-width: 1000px;
    width: 100%;
    text-align: center;
}

/* Accommodation Section */
.accommodation-section {
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4b879a 0%, #112736 100%);
    padding: 4rem 0;
}

.accommodation-content {
    max-width: 1000px;
    width: 100%;
    text-align: center;
}

.accommodation-content .section-title {
    color: #fff8ef;
}

.accommodation-image {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
}

.accommodation-image img {
    max-width: 600px;
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.accommodation-content .section-description {
    color: #fff8ef;
    opacity: 0.9;
}

/* Practical Info Section */
.practical-info-section {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff8ef;
    padding: 4rem 0;
}

.practical-info-content {
    max-width: 1000px;
    width: 100%;
    text-align: center;
}

.info-list {
    max-width: 800px;
    margin: 2rem auto;
    text-align: left;
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #112736;
}

.info-bullet {
    color: #4b879a;
    margin-right: 1rem;
    font-weight: bold;
    margin-top: 0.1rem;
}

.final-cta {
    text-align: center;
    margin-top: 3rem;
}

.retreat-book-button {
    display: inline-block;
    background: #d4a574;
    color: #112736;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 50px; /* Runder Button */
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
}

.retreat-book-button:hover {
    background: #c19660;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 165, 116, 0.4);
}

/* Retreat-Seite spezifische Überschriften in Senfgelb */
.retreat-page .section-title {
    color: #d4a574; /* Senfgelb nur für Retreat-Seite */
}

/* Responsive Design for Retreat Page */
@media (max-width: 768px) {
    /* Hero responsive für Retreat-Seite */
    .retreat-page .hero-content {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    .retreat-page .hero-text {
        padding: 60px 30px 40px 30px; /* Mehr Padding oben für Hamburger-Menü */
        text-align: center;
        align-items: center;
        flex: 0 0 auto;
        min-height: 45vh; /* Weniger Platz für Text, mehr für Bild */
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .retreat-page .main-title {
        font-size: clamp(2.2rem, 7vw, 3rem);
    }

    .retreat-page .subtitle {
        font-size: clamp(1.1rem, 3.5vw, 1.5rem);
        justify-content: center;
    }

    .retreat-page .hero-cta-button {
        padding: 0.9rem 1.8rem;
        font-size: 0.95rem;
    }

    .retreat-page .hero-image {
        flex: 1;
        min-height: 60vh; /* Höher für bessere Abdeckung */
        width: 100%;
        overflow: hidden;
    }

    .retreat-page .hero-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        min-height: 60vh; /* Mindesthöhe für vollständige Abdeckung */
    }

    /* Accordion responsive */
    .accordion-header {
        padding: 1rem 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        position: relative;
    }

    .day-number {
        font-size: 0.8rem;
    }

    .day-title {
        margin-left: 0;
        font-size: 1rem;
    }

    .accordion-icon {
        position: absolute;
        top: 1rem;
        right: 1.5rem;
    }

    .accordion-content p {
        padding: 1rem 1.5rem;
        font-size: 0.95rem;
    }

    /* Info list responsive */
    .info-item {
        font-size: 1rem;
        margin-bottom: 1.2rem;
    }

    .experience-text p {
        font-size: 1rem;
    }

    .schedule-intro {
        font-size: 1rem;
    }

    .retreat-book-button {
        padding: 0.9rem 1.8rem;
        font-size: 0.95rem;
    }

    .accommodation-image img {
        max-width: 400px;
        border-radius: 12px;
    }
}

@media (max-width: 480px) {
    /* Mobile accordion adjustments */
    .accordion-header {
        padding: 0.8rem 1rem;
    }

    .accordion-icon {
        top: 0.8rem;
        right: 1rem;
    }

    .day-number {
        font-size: 0.75rem;
    }

    .day-title {
        font-size: 0.9rem;
    }

    .accordion-content p {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }

    /* Mobile section adjustments */
    .experience-section,
    .connection-section,
    .practical-info-section,
    .accommodation-section {
        padding: 2rem 1rem; /* Mehr seitlicher Abstand */
    }

    .container {
        padding: 0 1rem; /* Zusätzlicher Container-Padding */
    }

    .section-title {
        font-size: 2rem; /* Kleinere Überschriften auf Mobile */
        margin-bottom: 1rem;
    }

    .section-description {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .experience-text p {
        font-size: 1rem;
    }

    .retreat-book-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .accommodation-image img {
        max-width: 300px;
        border-radius: 10px;
    }

    /* Hero responsive für Mobile */
    .retreat-page .hero-text {
        padding: 60px 20px 20px 20px; /* Mehr Padding oben für Hamburger-Menü */
        min-height: 40vh; /* Kompakter für kleine Bildschirme */
    }

    .retreat-page .main-title {
        font-size: clamp(1.8rem, 9vw, 2.5rem);
        margin-bottom: 10px; /* Weniger Abstand */
    }

    .retreat-page .subtitle {
        font-size: clamp(1rem, 4.5vw, 1.3rem);
        margin-bottom: 20px; /* Weniger Abstand */
    }

    .retreat-page .hero-cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .retreat-page .hero-image {
        min-height: 65vh; /* Höher für bessere Abdeckung auf kleinen Bildschirmen */
        overflow: hidden;
    }

    .retreat-page .hero-image img {
        min-height: 65vh;
    }

    .info-item {
        font-size: 0.95rem;
    }
}
